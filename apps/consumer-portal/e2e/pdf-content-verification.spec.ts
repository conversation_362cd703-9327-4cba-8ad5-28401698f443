import { test, expect } from '@playwright/test';

// Helper function to login
async function loginToPortal(page) {
  await page.goto('/login');
  await expect(page).toHaveTitle('Tallied Cardholder Portal - Tallied Internal Program');

  const loginButton = page.locator('text=Login with email').or(
    page.locator('button:has-text("Login with email")')
  );

  await expect(loginButton).toBeVisible({ timeout: 10000 });
  await loginButton.click();

  await page.waitForLoadState('networkidle', { timeout: 15000 });
  await expect(page).toHaveTitle('Welcome Back!');

  const usernameInput = page.locator('input[name="loginName"]');
  await expect(usernameInput).toBeVisible({ timeout: 10000 });
  await usernameInput.fill('<EMAIL>');

  const nextButton = page.locator('button[type="submit"]');
  await expect(nextButton).toBeVisible({ timeout: 5000 });
  await nextButton.click();

  await page.waitForLoadState('networkidle', { timeout: 10000 });

  const passwordInput = page.locator('input[type="password"]');
  await expect(passwordInput).toBeVisible({ timeout: 10000 });
  await passwordInput.fill('10Apples!');

  const submitButton = page.locator('button[type="submit"]');
  await expect(submitButton).toBeVisible({ timeout: 5000 });
  await submitButton.click();

  await page.waitForLoadState('networkidle', { timeout: 15000 });
  await expect(page).not.toHaveURL(/\/login$/);
}

test.describe('PDF Content Verification', () => {
  test.beforeEach(async ({ page }) => {
    // Set consistent desktop viewport for all PDF tests
    await page.setViewportSize({ width: 1920, height: 1080 });
    await loginToPortal(page);
  });

  test('should verify statement PDF contains correct headers and structure', async ({ page, context }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Navigate to statements tab
    const statementsTab = page.locator('button:has-text("Statements")').first();
    await expect(statementsTab).toBeVisible();
    await statementsTab.click();
    await page.waitForLoadState('networkidle', { timeout: 5000 });

    // Find statement PDF links
    const statementPdfLinks = page.locator('a[href*=".pdf"]').filter({ hasText: /June|July|August|September|October|November|December/ });
    const statementCount = await statementPdfLinks.count();

    if (statementCount > 0) {
      const firstStatementPdf = statementPdfLinks.first();
      await expect(firstStatementPdf).toBeVisible();

      // Set up listener for new page/tab
      const pagePromise = context.waitForEvent('page');

      // Click the PDF link
      await firstStatementPdf.click();

      // Wait for new page to open
      const newPage = await pagePromise;

      // Verify the PDF opened correctly first - handle URL encoding
      const pdfUrl = decodeURIComponent(newPage.url());
      console.log(`PDF URL: ${newPage.url()}`);
      console.log(`Decoded PDF URL: ${pdfUrl}`);
      expect(pdfUrl).toMatch(/\.pdf(\?|$)/); // Match .pdf followed by query params or end of string

      // Try to wait for load but don't fail if it times out
      try {
        await newPage.waitForLoadState('load', { timeout: 10000 });
      } catch (error) {
        console.log('PDF load state timeout (this is normal for large PDFs)');
      }

      // Wait for PDF to render
      await newPage.waitForTimeout(3000);

      try {
        // Try to extract text content from PDF
        const pdfText = await newPage.locator('body').textContent();

        if (pdfText && pdfText.length > 100) {
          console.log(`PDF text extracted successfully: ${pdfText.length} characters`);

          // Define expected statement headers and sections
          const expectedHeaders = [
            'Account Summary',
            'Payment Information',
            'Account Activity',
            'Fees and Interest Charges'
          ];

          const expectedAccountSummaryItems = [
            'Previous Balance',
            'Payments',
            'Purchases',
            'Balance Transfers',
            'Cash Advances',
            'Fees Charged',
            'Interest Charged',
            'New Balance'
          ];

          const expectedPaymentItems = [
            'New Balance',
            'Minimum Payment Due',
            'Payment Due Date'
          ];

          console.log('\n=== CHECKING STATEMENT HEADERS ===');
          for (const header of expectedHeaders) {
            const found = pdfText.includes(header);
            console.log(`${header}: ${found ? 'FOUND' : 'NOT FOUND'}`);

            if (found) {
              // If we find the header, verify it's in the right context
              expect(pdfText).toContain(header);
            }
          }

          console.log('\n=== CHECKING ACCOUNT SUMMARY SECTION ===');
          // Check if Account Summary section contains expected items
          const accountSummaryIndex = pdfText.indexOf('Account Summary');
          if (accountSummaryIndex !== -1) {
            // Get text after Account Summary header (next 1000 characters)
            const accountSummarySection = pdfText.substring(accountSummaryIndex, accountSummaryIndex + 1000);

            for (const item of expectedAccountSummaryItems) {
              const found = accountSummarySection.includes(item);
              console.log(`  ${item}: ${found ? 'FOUND in Account Summary' : 'not found in Account Summary'}`);
            }

            // Verify that Purchases appears in Account Summary, not Payment Information
            expect(accountSummarySection).toContain('Purchases');
          }

          console.log('\n=== CHECKING PAYMENT INFORMATION SECTION ===');
          // Check if Payment Information section contains expected items
          const paymentInfoIndex = pdfText.indexOf('Payment Information');
          if (paymentInfoIndex !== -1) {
            // Get text after Payment Information header (next 500 characters)
            const paymentInfoSection = pdfText.substring(paymentInfoIndex, paymentInfoIndex + 500);

            for (const item of expectedPaymentItems) {
              const found = paymentInfoSection.includes(item);
              console.log(`  ${item}: ${found ? 'FOUND in Payment Information' : 'not found in Payment Information'}`);
            }

            // Verify that Payment Information doesn't contain Purchases (should be in Account Summary)
            const containsPurchases = paymentInfoSection.includes('Purchases');
            console.log(`  Purchases incorrectly in Payment Information: ${containsPurchases ? 'YES (ERROR)' : 'NO (CORRECT)'}`);

            // This should NOT contain Purchases
            expect(paymentInfoSection).not.toContain('Purchases');
          }

          console.log('\n=== CHECKING FOR MONETARY AMOUNTS ===');
          // Check for dollar amounts (should be formatted correctly)
          const dollarAmountPattern = /\$[\d,]+\.\d{2}/g;
          const dollarAmounts = pdfText.match(dollarAmountPattern);
          console.log(`Found ${dollarAmounts?.length || 0} properly formatted dollar amounts`);

          if (dollarAmounts && dollarAmounts.length > 0) {
            console.log(`Sample amounts: ${dollarAmounts.slice(0, 5).join(', ')}`);
          }

          console.log('\n=== CHECKING FOR DATES ===');
          // Check for date formats
          const datePattern = /\d{1,2}\/\d{1,2}\/\d{4}/g;
          const dates = pdfText.match(datePattern);
          console.log(`Found ${dates?.length || 0} dates in MM/DD/YYYY format`);

          // Show first 500 characters for debugging
          console.log('\n=== FIRST 500 CHARACTERS OF PDF ===');
          console.log(pdfText.substring(0, 500));

        } else {
          console.log('Could not extract sufficient text from PDF or PDF is image-based');

          // Even if we can't extract text, verify the PDF opened
          const pdfUrl = decodeURIComponent(newPage.url());
          expect(pdfUrl).toMatch(/\.pdf(\?|$)/);

          // Take a screenshot for manual verification
          await newPage.screenshot({ path: 'test-results/statement-pdf-screenshot.png', fullPage: true });
          console.log('Screenshot saved for manual verification');
        }

      } catch (error) {
        console.log(`Error extracting PDF content: ${error.message}`);

        // Even if text extraction fails, verify the PDF opened correctly
        const pdfUrl = decodeURIComponent(newPage.url());
        expect(pdfUrl).toMatch(/\.pdf(\?|$)/);
        await newPage.screenshot({ path: 'test-results/statement-pdf-error.png', fullPage: true });
      }

      // Close the PDF page
      await newPage.close();

    } else {
      console.log('No statement PDF links found - test account may not have statements yet');
    }
  });

  test('should verify PDF link is present and accessible in statements tab', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Navigate to statements tab
    const statementsTab = page.locator('button:has-text("Statements")').first();
    await expect(statementsTab).toBeVisible();
    await statementsTab.click();
    await page.waitForLoadState('networkidle', { timeout: 5000 });

    // Look for PDF icon and statement information
    const pdfIcon = page.locator('.fa-file-pdf, [class*="pdf"], i:has-text("pdf")').first();

    // Check if PDF icon is visible (indicates PDF link presence)
    if (await pdfIcon.isVisible()) {
      await expect(pdfIcon).toBeVisible();
      console.log('✓ PDF icon found and visible');
    }

    // Look for statement links with date information (filter out legal documents)
    const statementLinks = page.locator('a[href*=".pdf"]').filter({ hasText: /June|July|August|September|October|November|December/ });
    const linkCount = await statementLinks.count();

    if (linkCount > 0) {
      console.log(`Found ${linkCount} statement PDF link(s)`);

      // Verify first statement link
      const firstLink = statementLinks.first();
      await expect(firstLink).toBeVisible();
      await expect(firstLink).toBeEnabled();

      // Verify link contains expected information
      const linkText = await firstLink.textContent();
      expect(linkText).toMatch(/\d{4}/); // Should contain year
      expect(linkText).toMatch(/\$/); // Should contain dollar amount

      console.log(`✓ Statement link text: "${linkText?.trim()}"`);

    } else {
      console.log('No statement PDF links found in current view');

      // Check if there are any PDF links at all (including hidden legal docs)
      const allPdfLinks = page.locator('a[href*=".pdf"]');
      const allCount = await allPdfLinks.count();
      console.log(`Found ${allCount} total PDF links (including hidden legal documents)`);
    }
  });
});
