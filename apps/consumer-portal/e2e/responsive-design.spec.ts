import { test, expect } from '@playwright/test';

// Test credentials
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = '10Apples!';

// Viewport configurations
const DESKTOP_VIEWPORT = { width: 1920, height: 1080 };
const MOBILE_VIEWPORT = { width: 2868, height: 1320 }; // iPhone 16 Pro Max

async function loginToPortal(page) {
  await page.goto('/login');
  await expect(page).toHaveTitle('Tallied Cardholder Portal - Tallied Internal Program');

  const loginButton = page.locator('text=Login with email').or(
    page.locator('button:has-text("Login with email")')
  );

  await expect(loginButton).toBeVisible({ timeout: 5000 });
  await loginButton.click();

  await page.waitForLoadState('networkidle', { timeout: 8000 });
  await expect(page).toHaveTitle('Welcome Back!');

  const usernameInput = page.locator('input[name="loginName"]');
  await expect(usernameInput).toBeVisible({ timeout: 5000 });
  await usernameInput.fill(TEST_EMAIL);

  const nextButton = page.locator('button[type="submit"]');
  await expect(nextButton).toBeVisible({ timeout: 3000 });
  await nextButton.click();

  await page.waitForLoadState('networkidle', { timeout: 5000 });

  const passwordInput = page.locator('input[type="password"]');
  await expect(passwordInput).toBeVisible({ timeout: 5000 });
  await passwordInput.fill(TEST_PASSWORD);

  const submitButton = page.locator('button[type="submit"]');
  await expect(submitButton).toBeVisible({ timeout: 3000 });
  await submitButton.click();

  await page.waitForLoadState('networkidle', { timeout: 8000 });
  await expect(page).not.toHaveURL(/\/login$/);
}

test.describe('Responsive Design Tests - Desktop (1920x1080)', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize(DESKTOP_VIEWPORT);
    await loginToPortal(page);
  });

  test('should display desktop navigation correctly', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Desktop navigation should be visible directly (not in hamburger menu)
    const homeNav = page.locator('a:has-text("Home")').first();
    const paymentsNav = page.locator('a:has-text("Payments")').first();
    const rewardsNav = page.locator('a:has-text("Rewards")').first();

    await expect(homeNav).toBeVisible();
    await expect(paymentsNav).toBeVisible();
    await expect(rewardsNav).toBeVisible();

    // Verify navigation links work
    await expect(homeNav).toHaveAttribute('href', '/');
    await expect(paymentsNav).toHaveAttribute('href', '/payments');
    await expect(rewardsNav).toHaveAttribute('href', '/rewards');
  });

  test('should navigate between all pages on desktop', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Test navigation to Payments
    const paymentsNav = page.locator('a:has-text("Payments")').first();
    await expect(paymentsNav).toBeVisible();
    await paymentsNav.click();
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL(/\/payments/);

    // Test navigation to Rewards
    const rewardsNav = page.locator('a:has-text("Rewards")').first();
    await expect(rewardsNav).toBeVisible();
    await rewardsNav.click();
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL(/\/rewards/);

    // Test navigation back to Home
    const homeNav = page.locator('a:has-text("Home")').first();
    await expect(homeNav).toBeVisible();
    await homeNav.click();
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL('/');
  });

  test('should display and switch tabs on desktop', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Verify tabs are visible
    const recentActivityTab = page.locator('button:has-text("Recent Activity"), [role="tab"]:has-text("Recent Activity")').first();
    const statementsTab = page.locator('button:has-text("Statements"), [role="tab"]:has-text("Statements")').first();

    await expect(recentActivityTab).toBeVisible();
    await expect(statementsTab).toBeVisible();

    // Test tab switching
    await statementsTab.click();
    await page.waitForLoadState('networkidle', { timeout: 5000 });
    await expect(statementsTab).toBeVisible();

    await recentActivityTab.click();
    await page.waitForLoadState('networkidle', { timeout: 5000 });
    await expect(recentActivityTab).toBeVisible();
  });

  test('should verify PDF functionality on desktop', async ({ page, context }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    const statementsTab = page.locator('button:has-text("Statements")').first();
    await expect(statementsTab).toBeVisible();
    await statementsTab.click();
    await page.waitForLoadState('networkidle', { timeout: 5000 });

    const statementPdfLinks = page.locator('a[href*=".pdf"]').filter({ hasText: /June|July|August|September|October|November|December/ });
    const statementCount = await statementPdfLinks.count();

    if (statementCount > 0) {
      const firstStatementPdf = statementPdfLinks.first();
      await expect(firstStatementPdf).toBeVisible();

      const pagePromise = context.waitForEvent('page');
      await firstStatementPdf.click();
      const newPage = await pagePromise;

      const pdfUrl = decodeURIComponent(newPage.url());
      console.log(`Desktop PDF URL: ${pdfUrl}`);
      expect(pdfUrl).toMatch(/\.pdf(\?|$)/);

      await newPage.close();
    } else {
      console.log('No statement PDF links found for desktop test');
    }
  });
});

test.describe('Responsive Design Tests - Mobile (2868x1320)', () => {
  test.beforeEach(async ({ page }) => {
    await page.setViewportSize(MOBILE_VIEWPORT);
    await loginToPortal(page);
  });

  test('should display mobile navigation with hamburger menu', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // On mobile, navigation should be in hamburger menu
    // Look for hamburger menu button (common selectors)
    const hamburgerMenu = page.locator('button[aria-label*="menu" i], button[aria-label*="navigation" i], .hamburger, .menu-toggle, button:has(.fa-bars), button:has(.menu-icon)').first();

    // If hamburger menu exists, test it
    if (await hamburgerMenu.isVisible()) {
      await hamburgerMenu.click();
      await page.waitForTimeout(500); // Wait for menu animation

      // Navigation items should now be visible in the menu
      const homeNav = page.locator('a:has-text("Home")').first();
      const paymentsNav = page.locator('a:has-text("Payments")').first();
      const rewardsNav = page.locator('a:has-text("Rewards")').first();

      await expect(homeNav).toBeVisible();
      await expect(paymentsNav).toBeVisible();
      await expect(rewardsNav).toBeVisible();
    } else {
      // If no hamburger menu, navigation might still be visible on this mobile size
      const homeNav = page.locator('a:has-text("Home")').first();
      const paymentsNav = page.locator('a:has-text("Payments")').first();
      const rewardsNav = page.locator('a:has-text("Rewards")').first();

      await expect(homeNav).toBeVisible();
      await expect(paymentsNav).toBeVisible();
      await expect(rewardsNav).toBeVisible();
    }
  });

  test('should navigate between pages on mobile', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Helper function to access navigation on mobile
    async function accessMobileNavigation() {
      const hamburgerMenu = page.locator('button[aria-label*="menu" i], button[aria-label*="navigation" i], .hamburger, .menu-toggle, button:has(.fa-bars), button:has(.menu-icon)').first();
      if (await hamburgerMenu.isVisible()) {
        await hamburgerMenu.click();
        await page.waitForTimeout(500);
      }
    }

    // Navigate to Payments
    await accessMobileNavigation();
    const paymentsNav = page.locator('a:has-text("Payments")').first();
    await expect(paymentsNav).toBeVisible();
    await paymentsNav.click();
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL(/\/payments/);

    // Navigate to Rewards
    await accessMobileNavigation();
    const rewardsNav = page.locator('a:has-text("Rewards")').first();
    await expect(rewardsNav).toBeVisible();
    await rewardsNav.click();
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL(/\/rewards/);

    // Navigate back to Home
    await accessMobileNavigation();
    const homeNav = page.locator('a:has-text("Home")').first();
    await expect(homeNav).toBeVisible();
    await homeNav.click();
    await page.waitForLoadState('networkidle');
    await expect(page).toHaveURL('/');
  });

  test('should display and switch tabs on mobile', async ({ page }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Tabs should still be visible on mobile
    const recentActivityTab = page.locator('button:has-text("Recent Activity"), [role="tab"]:has-text("Recent Activity")').first();
    const statementsTab = page.locator('button:has-text("Statements"), [role="tab"]:has-text("Statements")').first();

    await expect(recentActivityTab).toBeVisible();
    await expect(statementsTab).toBeVisible();

    // Test tab switching on mobile
    await statementsTab.click();
    await page.waitForLoadState('networkidle', { timeout: 5000 });
    await expect(statementsTab).toBeVisible();

    await recentActivityTab.click();
    await page.waitForLoadState('networkidle', { timeout: 5000 });
    await expect(recentActivityTab).toBeVisible();
  });

  test('should verify PDF functionality on mobile', async ({ page, context }) => {
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    const statementsTab = page.locator('button:has-text("Statements")').first();
    await expect(statementsTab).toBeVisible();
    await statementsTab.click();
    await page.waitForLoadState('networkidle', { timeout: 5000 });

    const statementPdfLinks = page.locator('a[href*=".pdf"]').filter({ hasText: /June|July|August|September|October|November|December/ });
    const statementCount = await statementPdfLinks.count();

    if (statementCount > 0) {
      const firstStatementPdf = statementPdfLinks.first();
      await expect(firstStatementPdf).toBeVisible();

      const pagePromise = context.waitForEvent('page');
      await firstStatementPdf.click();
      const newPage = await pagePromise;

      const pdfUrl = decodeURIComponent(newPage.url());
      console.log(`Mobile PDF URL: ${pdfUrl}`);
      expect(pdfUrl).toMatch(/\.pdf(\?|$)/);

      await newPage.close();
    } else {
      console.log('No statement PDF links found for mobile test');
    }
  });
});

test.describe('Responsive Design Tests - Viewport Switching', () => {
  test.beforeEach(async ({ page }) => {
    await loginToPortal(page);
  });

  test('should handle viewport changes correctly', async ({ page }) => {
    // Start with desktop viewport
    await page.setViewportSize(DESKTOP_VIEWPORT);
    await page.goto('/');
    await page.waitForLoadState('networkidle');

    // Verify desktop navigation is visible
    const homeNavDesktop = page.locator('a:has-text("Home")').first();
    await expect(homeNavDesktop).toBeVisible();

    // Switch to mobile viewport
    await page.setViewportSize(MOBILE_VIEWPORT);
    await page.waitForTimeout(1000); // Wait for responsive changes

    // Navigation should adapt to mobile layout
    // Either hamburger menu appears or navigation remains visible
    const hamburgerMenu = page.locator('button[aria-label*="menu" i], button[aria-label*="navigation" i], .hamburger, .menu-toggle, button:has(.fa-bars), button:has(.menu-icon)').first();
    const homeNavMobile = page.locator('a:has-text("Home")').first();

    const hasHamburgerMenu = await hamburgerMenu.isVisible();
    const hasDirectNav = await homeNavMobile.isVisible();

    // Either hamburger menu should be visible OR direct navigation should be visible
    expect(hasHamburgerMenu || hasDirectNav).toBe(true);

    // Switch back to desktop viewport
    await page.setViewportSize(DESKTOP_VIEWPORT);
    await page.waitForTimeout(1000); // Wait for responsive changes

    // Desktop navigation should be restored
    const homeNavRestored = page.locator('a:has-text("Home")').first();
    await expect(homeNavRestored).toBeVisible();
  });
});
